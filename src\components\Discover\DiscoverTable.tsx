import React, { useState, useCallback } from 'react';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';
import { useSearch } from '../../hooks/dataHooks';
import { useFieldFormat } from '../../hooks/uiHooks';
import { useDiscoverLogs } from '../../hooks/useDiscoverLogs';
import { LogEntry } from '../../types/discover';


import PaginationControls from './PaginationControls';
import PageSizeSelector from './PageSizeSelector';
import './DiscoverTable.css';

interface DiscoverTableProps {
  className?: string;
  pageSize?: number;
  showPagination?: boolean;
  showExpandedView?: boolean;
}

/**
 * DiscoverTable component that displays log data with pagination and expandable rows
 */
const DiscoverTable: React.FC<DiscoverTableProps> = ({
  className = '',
  showPagination = true,
  showExpandedView = true
}) => {
  console.log('[DiscoverTable] Props received:', { showExpandedView, showPagination, className });

  const { state, dispatch } = useDiscover();
  const { /* results, */ isLoading, error } = useSearch();
  const { getLogLevelColor } = useDiscoverLogs();

  // State for expanded rows
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  // Format functions
  const { formatValue: formatDate } = useFieldFormat('date');



  // Calculate pagination
  const totalPages = Math.ceil(state.filteredData.length / state.pagination.pageSize);
  const startIndex = (state.pagination.currentPage - 1) * state.pagination.pageSize;
  const endIndex = Math.min(startIndex + state.pagination.pageSize, state.filteredData.length);
  const currentPageData = state.filteredData.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    dispatch(discoverActions.setCurrentPage(page));
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    dispatch(discoverActions.setPageSize(size));
  };
  
  // Toggle row expansion
  const toggleRowExpansion = (id: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  
  
  // Get field value from log entry
  const getFieldValue = (log: LogEntry, fieldPath: string): unknown => {
    const parts = fieldPath.split('.');
    let value: unknown = log;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = (value as Record<string, unknown>)[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  };
  
  // Format field value for display
  const formatFieldValue = useCallback((value: unknown, fieldName: string): string => {
    if (value === undefined || value === null) {
      return '-';
    }
    
    if (fieldName === 'timestamp' || fieldName.endsWith('.timestamp')) {
      return formatDate(value);
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }, [formatDate]);
  

  
  // Expanded row details component
  const ExpandedRowDetails = useCallback(({ log }: { log: LogEntry }) => {
    const [activeTab, setActiveTab] = useState<'json' | 'table'>('table');

    // Format JSON for display
    const formattedJson = JSON.stringify(log, null, 2);

    // Get all fields from the log entry
    const getLogFields = () => {
      const fields: Array<{ key: string; value: any }> = [];

      // Add base fields
      fields.push({ key: 'timestamp', value: log.timestamp });
      fields.push({ key: 'level', value: log.level });
      fields.push({ key: 'source', value: log.source });
      fields.push({ key: 'message', value: log.message });

      // Add any additional fields from the log object
      Object.entries(log).forEach(([key, value]) => {
        if (!['id', 'timestamp', 'level', 'source', 'message'].includes(key)) {
          fields.push({ key, value });
        }
      });

      return fields;
    };

    const fields = getLogFields();

    return (
      <div
        role="region"
        aria-label="Log entry details"
        style={{
          padding: '16px',
          background: 'rgba(0, 0, 0, 0.2)',
          borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
          color: 'white',
          fontSize: '14px',
        }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px',
      }}>
        <h4 style={{ margin: 0 }}>Log Details</h4>

        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          {/* Tab navigation */}
          <div role="tablist" style={{ display: 'flex', gap: '8px' }}>
            <button
              role="tab"
              aria-selected={activeTab === 'table'}
              aria-controls="table-panel"
              id="table-tab"
              onClick={() => setActiveTab('table')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'table' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'table' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              Table View
            </button>
            <button
              role="tab"
              aria-selected={activeTab === 'json'}
              aria-controls="json-panel"
              id="json-tab"
              onClick={() => setActiveTab('json')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'json' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'json' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              JSON View
            </button>
          </div>

          {/* Close button */}
          <button
            onClick={() => {/* We'll add close functionality later */}}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.7)',
              cursor: 'pointer',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Table view */}
        <div
          role="tabpanel"
          id="table-panel"
          aria-labelledby="table-tab"
          hidden={activeTab !== 'table'}
          style={{
            display: activeTab === 'table' ? 'grid' : 'none',
            gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
            gap: '8px',
            maxHeight: '400px',
            overflowY: 'auto',
          }}
        >
          {fields.map(({ key, value }) => (
            <React.Fragment key={key}>
              <div style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '12px',
                fontWeight: 'bold',
                padding: '4px 8px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              }}>
                {key}
              </div>
              <div style={{
                color: 'white',
                fontSize: '12px',
                padding: '4px 8px',
                wordBreak: 'break-word',
              }}>
                {formatFieldValue(value, key)}
              </div>
            </React.Fragment>
          ))}
        </div>

        {/* JSON view */}
        <pre
          role="tabpanel"
          id="json-panel"
          aria-labelledby="json-tab"
          hidden={activeTab !== 'json'}
          style={{
            display: activeTab === 'json' ? 'block' : 'none',
            background: 'rgba(0, 0, 0, 0.3)',
            padding: '12px',
            borderRadius: '4px',
            overflowX: 'auto',
            maxHeight: '400px',
            fontSize: '12px',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            color: '#00e5ff',
            lineHeight: '1.4',
          }}
        >
          {formattedJson}
        </pre>
      </div>
    );
  }, [formatFieldValue]);
  

  
  // Render pagination controls
  const renderPagination = () => {
    if (!showPagination) return null;

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px 0',
        background: 'rgba(10, 14, 23, 0.8)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
      }}>
        <PaginationControls
          currentPage={state.pagination.currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onFirstPage={() => handlePageChange(1)}
          onPreviousPage={() => handlePageChange(state.pagination.currentPage - 1)}
          onNextPage={() => handlePageChange(state.pagination.currentPage + 1)}
          onLastPage={() => handlePageChange(totalPages)}
          isFirstPage={state.pagination.currentPage === 1}
          isLastPage={state.pagination.currentPage === totalPages}
          isLoading={isLoading}
          noResults={state.filteredData.length === 0}
        />
      </div>
    );
  };
  
  console.log('[DiscoverTable] Render state:', {
    dataLength: state.filteredData.length,
    showExpandedView,
    selectedFields: state.selectedFields,
    isLoading,
    hasError: !!error
  });
  
  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {isLoading && (
        <div className="table-loading">
          <p>Loading...</p>
        </div>
      )}
      
      {error && (
        <div className="table-error">
          <p>Error: {error.message}</p>
        </div>
      )}
      
      {!isLoading && !error && state.filteredData.length === 0 && (
        <div className="table-empty">
          <p>No data found</p>
        </div>
      )}
      
      {!isLoading && !error && state.filteredData.length > 0 && (
        <>
          {/* Scrollable table area (header + body) */}
          <div style={{
            flex: 1,
            minHeight: 0,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}>
            {/* Table header */}
            <div
              role="row"
              aria-rowindex={1}
              style={{
                display: 'flex',
                borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
                background: 'rgba(10, 14, 23, 0.8)',
                padding: '8px 16px',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '14px',
                position: 'sticky',
                top: 0,
                zIndex: 10,
              }}
            >
              <div style={{ width: '30px' }} role="columnheader" aria-hidden="true"></div>
              {state.selectedFields.map(field => (
                <div
                  key={field}
                  role="columnheader"
                  style={{
                    flexGrow: field === 'message' ? 3 : 1,
                    padding: '0 8px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {field}
                </div>
              ))}
            </div>

            {/* Table body with virtual scrolling */}
            <div
              style={{
                flex: 1,
                minHeight: 0,
                overflowY: 'auto',
                position: 'relative',
              }}
            >
              {currentPageData.map((log, index) => {
                if (index === 0) {
                  console.log('[DiscoverTable] Rendering first row with showExpandedView:', showExpandedView, 'log:', log);
                }
                return (
                <React.Fragment key={log.id}>
                  <div
                    role="row"
                    aria-expanded={expandedRows[log.id]}
                    className={`log-row ${expandedRows[log.id] ? 'expanded' : ''}`}
                    onClick={showExpandedView ? () => toggleRowExpansion(log.id) : undefined}
                    style={{
                      display: 'flex',
                      borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                      padding: '8px 16px',
                      color: 'white',
                      fontSize: '14px',
                      cursor: showExpandedView ? 'pointer' : 'default',
                      background: expandedRows[log.id] ? 'rgba(0, 229, 255, 0.05)' : 'transparent',
                      transition: 'background-color 0.2s',
                      alignItems: 'center',
                    }}
                  >
                    <div style={{ width: '30px', display: 'flex', alignItems: 'center' }}>
                      {showExpandedView && (
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="#00e5ff"
                          strokeWidth="2"
                          style={{
                            transform: expandedRows[log.id] ? 'rotate(90deg)' : 'rotate(0deg)',
                            transition: 'transform 0.2s',
                            marginRight: '8px',
                          }}
                        >
                          <polyline points="9,18 15,12 9,6"></polyline>
                        </svg>
                      )}
                    </div>
                    {state.selectedFields.map(field => {
                      const value = getFieldValue(log, field);
                      const formattedValue = formatFieldValue(value, field);

                      // Special styling for level field
                      if (field === 'level') {
                        const levelColor = getLogLevelColor(value as 'info' | 'warning' | 'error' | 'critical');
                        return (
                          <div
                            key={`${log.id}-${field}`}
                            role="cell"
                            style={{
                              flexGrow: 1,
                              padding: '0 8px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              color: levelColor,
                            }}
                          >
                            {formattedValue}
                          </div>
                        );
                      }

                      return (
                        <div
                          key={`${log.id}-${field}`}
                          role="cell"
                          style={{
                            flexGrow: field === 'message' ? 3 : 1,
                            padding: '0 8px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                          }}
                        >
                          {formattedValue}
                        </div>
                      );
                    })}
                  </div>
                  {showExpandedView && expandedRows[log.id] && (
                    <div className="expanded-row-details">
                      <ExpandedRowDetails log={log} />
                    </div>
                  )}
                </React.Fragment>
              );
              })}
            </div>
          </div>

          {renderPagination()}

          {/* Status bar (always visible at very bottom) */}
          <div style={{
            padding: '8px 16px',
            borderTop: '1px solid rgba(0, 229, 255, 0.2)',
            background: 'rgba(10, 14, 23, 0.8)',
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '12px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            <div>
              {state.filteredData.length === 0 ? (
                <span style={{ color: 'rgba(255, 255, 255, 0.5)', fontStyle: 'italic' }}>
                  No logs match your current filters
                </span>
              ) : (
                `${state.filteredData.length} logs found`
              )}
            </div>
            <PageSizeSelector
              pageSize={state.pagination.pageSize}
              onPageSizeChange={handlePageSizeChange}
              options={[10, 25, 50, 100]}
              noResults={state.filteredData.length === 0}
            />
            <div>
              Showing {startIndex + 1} to {endIndex} of {state.filteredData.length} entries
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DiscoverTable;